import { DBOS } from '@dbos-inc/dbos-sdk';
import { ChatService } from './core/ChatService';
import { GeminiLLMService } from './services/GeminiLLMService';
import { LLMServiceFactory } from './services/LLMService';
import { WebInterface } from './interfaces/WebInterface';
import { StreamingChatService } from './streaming';
import { SessionCleanupService } from './core/SessionCleanupService';
import { ForaChat } from './operations';
import config, { validateConfig } from './config';
import WebSocket from 'ws';
import http from 'http';
import fs from 'fs';
import path from 'path';
import winston from 'winston';
// Import operations to ensure workflow queues are initialized before DBOS launch
import './operations';

export class ForaChatApp {
  private chatService: ChatService;
  private webInterface: WebInterface;
  private streamingService: StreamingChatService;
  private server: http.Server | null = null;
  private wss: WebSocket.Server | null = null;
  private isInitialized: boolean = false;
  private quietMode: boolean = false;
  private logStream: fs.WriteStream | null = null;

  constructor(options: { quietMode?: boolean } = {}) {
    // Validate configuration
    validateConfig();

    // Set quiet mode
    this.quietMode = options.quietMode || false;

    // Set up file logging in quiet mode
    if (this.quietMode) {
      console.log('🔧 Setting up file logging in quiet mode...');
      this.setupFileLogging();
      console.log('✅ File logging setup complete');
    }

    // Register LLM providers
    LLMServiceFactory.register('gemini', () => new GeminiLLMService());

    // Initialize services
    const llmService = LLMServiceFactory.create('gemini');
    this.chatService = new ChatService(llmService);
    this.webInterface = new WebInterface(this.chatService);
    this.streamingService = new StreamingChatService(this.chatService);
  }

  private setupFileLogging(): void {
    const logFilePath = path.join(process.cwd(), 'server.log');
    this.logStream = fs.createWriteStream(logFilePath, { flags: 'a' });

    // Store original streams and console methods
    const originalStdoutWrite = process.stdout.write;
    const originalStderrWrite = process.stderr.write;
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;
    const originalConsoleInfo = console.info;

    const writeToFile = (level: string, ...args: any[]) => {
      const timestamp = new Date().toISOString();
      const message = `[${timestamp}] [${level}] ${args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ')}\n`;

      if (this.logStream) {
        this.logStream.write(message);
      }
    };

    // Redirect stdout and stderr to file
    process.stdout.write = (chunk: any, encoding?: any, callback?: any) => {
      if (this.logStream) {
        const timestamp = new Date().toISOString();
        const message = `[${timestamp}] [STDOUT] ${chunk}`;
        this.logStream.write(message);
      }
      if (typeof encoding === 'function') {
        encoding();
      } else if (typeof callback === 'function') {
        callback();
      }
      return true;
    };

    process.stderr.write = (chunk: any, encoding?: any, callback?: any) => {
      if (this.logStream) {
        const timestamp = new Date().toISOString();
        const message = `[${timestamp}] [STDERR] ${chunk}`;
        this.logStream.write(message);
      }
      if (typeof encoding === 'function') {
        encoding();
      } else if (typeof callback === 'function') {
        callback();
      }
      return true;
    };

    // Redirect console methods to file in quiet mode
    console.log = (...args: any[]) => writeToFile('INFO', ...args);
    console.error = (...args: any[]) => writeToFile('ERROR', ...args);
    console.warn = (...args: any[]) => writeToFile('WARN', ...args);
    console.info = (...args: any[]) => writeToFile('INFO', ...args);

    // Store original methods for potential restoration
    (this as any).originalConsole = {
      log: originalConsoleLog,
      error: originalConsoleError,
      warn: originalConsoleWarn,
      info: originalConsoleInfo,
      stdoutWrite: originalStdoutWrite,
      stderrWrite: originalStderrWrite
    };
  }

  private setupWinstonFileLogging(): void {
    // Configure Winston to add file transport after DBOS initialization
    const logFilePath = path.join(process.cwd(), 'server.log');

    // Create a file transport for Winston
    const fileTransport = new winston.transports.File({
      filename: logFilePath,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message }) => {
          return `[${timestamp}] [${level.toUpperCase()}] ${message}`;
        })
      )
    });

    // Try to access the DBOS logger and add file transport
    try {
      // Access the internal Winston logger used by DBOS
      const dbosLogger = (DBOS as any).logger;
      if (dbosLogger && dbosLogger.add) {
        dbosLogger.add(fileTransport);
        console.log('✅ Added file transport to DBOS logger');
      }
    } catch (error) {
      console.log('⚠️ Could not add file transport to DBOS logger, using console override only');
    }
  }

  private log(message: string): void {
    if (!this.quietMode) {
      console.log(message);
    } else {
      // In quiet mode, logs go to file via overridden console methods
      console.log(message);
    }
  }



  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Configure DBOS
    DBOS.setConfig({
      name: "forachat",
      databaseUrl: config.database.url,
      userDbclient: "knex" as any,
      logLevel: this.quietMode ? "warn" : "info",
    });

    // Launch DBOS with the web interface
    await DBOS.launch({ expressApp: this.webInterface.getApp() });
    this.log("✅ DBOS Launched successfully");

    // Set up Winston file logging after DBOS is initialized
    if (this.quietMode) {
      this.setupWinstonFileLogging();
    }

    // Start session cleanup service (runs every hour)
    SessionCleanupService.startCleanupService(60);
    this.log("✅ Session cleanup service started");

    this.isInitialized = true;
  }

  async start(): Promise<void> {
    await this.initialize();

    const app = this.webInterface.getApp();

    // Create HTTP server
    this.server = http.createServer(app);

    // Create WebSocket server
    this.wss = new WebSocket.Server({ server: this.server });

    // Handle WebSocket connections
    this.wss.on('connection', (ws: WebSocket, req: http.IncomingMessage) => {
      this.handleWebSocketConnection(ws, req);
    });

    this.server.listen(config.port, () => {
      this.log(`🚀 ForaChat server is running on http://localhost:${config.port}`);
      this.log(`📊 Environment: ${config.environment}`);
      this.log(`🤖 LLM Provider: ${config.llm.model}`);
      this.log('\n📚 Available endpoints:');
      this.log('  GET  / - Web UI');
      this.log('  POST /chat - Send a message');
      this.log('  GET  /health - Health check');
      this.log('  POST /conversation/:id/message - Continue conversation');
      this.log('  GET  /conversation/:id - Get conversation history');
      this.log('  WebSocket - Real-time chat interface');
    });
  }

  getChatService(): ChatService {
    return this.chatService;
  }

  getWebInterface(): WebInterface {
    return this.webInterface;
  }

  getStreamingService(): StreamingChatService {
    return this.streamingService;
  }



  private async handleWebSocketConnection(ws: WebSocket, req: http.IncomingMessage): Promise<void> {
    try {
      // Parse cookies from WebSocket request
      const cookies = this.parseCookies(req.headers.cookie || '');
      let sessionId = cookies.forachat_session;
      let session = null;
      const userIdentifier = req.socket.remoteAddress || 'unknown';

      this.log(`🔗 WebSocket connection attempt from ${userIdentifier}, cookie sessionId: ${sessionId ? sessionId.substring(0, 8) + '...' : 'none'}`);

      if (sessionId) {
        // Try to get existing session
        const handle = await DBOS.startWorkflow(ForaChat).getSession(sessionId);
        session = await handle.getResult();

        if (session) {
          this.log(`✅ WebSocket reusing existing session ${sessionId.substring(0, 8)}... for user ${session.user_identifier}`);
        } else {
          this.log(`❌ WebSocket session ${sessionId.substring(0, 8)}... not found or expired, creating new session`);
        }
      } else {
        this.log(`🆕 WebSocket no session cookie found, creating new session for ${userIdentifier}`);
      }

      if (!session) {
        // Create new session for WebSocket
        sessionId = this.generateSessionId();

        const sessionRequest = {
          userIdentifier: `web_${userIdentifier}`,
          channel: 'web' as const,
          metadata: {
            userAgent: req.headers['user-agent'],
            ip: req.socket.remoteAddress
          }
        };

        const handle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
        session = await handle.getResult();
        sessionId = session.id;
        this.log(`🆕 Created new WebSocket session ${sessionId.substring(0, 8)}... for user ${session.user_identifier}`);
      } else {
        // Update session activity
        await DBOS.startWorkflow(ForaChat).updateSessionActivity(sessionId);
      }

      this.streamingService.createSession(sessionId, ws, session);
    } catch (error) {
      console.error(`Error handling WebSocket connection: ${(error as Error).message}`);
      ws.close(1011, 'Internal server error');
    }
  }

  private parseCookies(cookieHeader: string): Record<string, string> {
    const cookies: Record<string, string> = {};
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
    return cookies;
  }

  private generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  async shutdown(): Promise<void> {
    this.log('🛑 Shutting down ForaChat...');

    // Stop session cleanup service
    SessionCleanupService.stopCleanupService();

    // Close WebSocket server
    if (this.wss) {
      this.wss.close();
    }

    // Close HTTP server
    if (this.server) {
      this.server.close();
    }

    // Close log stream if in quiet mode
    if (this.quietMode && this.logStream) {
      this.logStream.end();
    }

    process.exit(0);
  }
}

// Graceful shutdown handling
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});
