const { ForaChatApp } = require('./dist/ForaChatApp');

async function testLogging() {
    console.log('Testing file logging...');

    // Create app in quiet mode
    const app = new ForaChatApp({ quietMode: true });

    // Wait a moment for setup to complete
    await new Promise(resolve => setTimeout(resolve, 100));

    // Test console logging
    console.log('This should go to server.log');
    console.error('This error should go to server.log');
    console.warn('This warning should go to server.log');

    console.log('Check server.log file for the logged messages');

    // Wait a moment for file writes
    setTimeout(() => {
        process.exit(0);
    }, 1000);
}

testLogging().catch(console.error);
